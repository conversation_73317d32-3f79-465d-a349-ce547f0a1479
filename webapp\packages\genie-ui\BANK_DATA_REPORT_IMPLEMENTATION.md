# 银行数据报告流式数据处理实现

## 概述

本实现为 `messageType === "agent_stream"` 且 `toolName === "bank_data_report"` 的流式数据提供了完整的处理方案，支持在对话区域和工作空间中实时展示银行数据分析报告。

## 实现方案

### 1. 数据流处理 (`utils/chat.ts`)

#### 新增消息类型处理
- 在 `combineData` 函数中添加了 `agent_stream` 消息类型的处理
- 新增 `handleAgentStreamMessage` 函数专门处理 agent_stream 消息
- 新增 `handleBankDataReportStream` 函数专门处理银行数据报告的流式数据

#### 核心逻辑
```typescript
// 检测是否为银行数据报告
if (eventData.resultMap?.toolResult?.toolName === "bank_data_report") {
  handleBankDataReportStream(eventData, currentChat, taskIndex);
}
```

#### 流式数据累积
- 自动累积流式数据到 `resultMap.answer` 字段
- 支持实时更新 `isFinal` 状态
- 创建特殊的 `messageType: "bank_data_report"` 用于标识

### 2. 工作空间展示 (`components/ActionPanel/`)

#### useMsgTypes.ts
- 添加 `useBankDataReport` 标识符
- 支持银行数据报告类型的识别

#### useContent.ts  
- 添加 `bank_data_report` 消息类型的内容处理
- 优先使用 `resultMap.answer`，回退到 `toolResult.toolResult`

#### ActionPanel.tsx
- 添加银行数据报告的渲染逻辑
- 使用 `MarkdownRenderer` 展示流式数据

### 3. 任务动作显示 (`buildAction` 函数)

- 添加银行数据报告的动作描述：
  - action: "正在生成银行数据分析报告"
  - tool: "银行数据分析"

## 数据结构

### 输入数据格式
```typescript
{
  messageType: "agent_stream",
  resultMap: {
    toolResult: {
      toolName: "bank_data_report",
      toolResult: "流式数据内容...",
      toolParam: {
        query: "用户查询"
      }
    },
    isFinal: false // 是否为最终数据
  }
}
```

### 处理后的任务格式
```typescript
{
  messageType: "bank_data_report",
  resultMap: {
    messageType: "bank_data_report", 
    answer: "累积的流式数据内容...",
    isFinal: false
  },
  toolResult: {
    toolName: "bank_data_report",
    toolResult: "当前流式数据片段..."
  }
}
```

## 展示效果

### 对话区域
- 显示任务进度："正在生成银行数据分析报告"
- 工具标识："银行数据分析"

### 工作空间
- 实时展示流式数据内容
- 使用 Markdown 格式渲染
- 支持滚动到底部查看最新内容

## 使用方式

1. 当后端发送 `messageType: "agent_stream"` 且 `toolName: "bank_data_report"` 的数据时
2. 系统自动识别并创建银行数据报告任务
3. 流式数据会实时累积并在工作空间中展示
4. 用户可以在对话区域看到任务进度，在工作空间看到详细内容

## 测试

可以使用 `utils/test-bank-data-report.ts` 中的测试函数验证实现：

```typescript
import { testBankDataReportHandling } from './utils/test-bank-data-report';
testBankDataReportHandling();
```

## 扩展性

该实现具有良好的扩展性：
- 可以轻松添加其他工具的流式数据处理
- 支持不同的渲染方式（HTML、JSON等）
- 可以自定义任务动作描述和工具标识
