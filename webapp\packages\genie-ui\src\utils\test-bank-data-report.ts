// 测试银行数据报告流式数据处理的示例代码
// 这个文件用于验证我们的实现是否正确

import { combineData } from './chat';

// 模拟银行数据报告的流式数据
const mockBankDataReportStream = {
  messageType: "agent_stream",
  messageId: "msg_001",
  taskId: "task_001", 
  taskOrder: 1,
  messageOrder: 1,
  resultMap: {
    messageType: "agent_stream",
    isFinal: false,
    toolResult: {
      toolName: "bank_data_report",
      toolResult: "## 银行数据分析报告\n\n### 概述\n本报告基于最新的银行业务数据进行分析...",
      toolParam: {
        query: "银行数据分析"
      }
    },
    requestId: "req_001"
  }
};

// 模拟当前聊天对象
const mockCurrentChat = {
  query: "请分析银行数据",
  files: [],
  responseType: "txt",
  sessionId: "session_001",
  requestId: "req_001",
  loading: true,
  forceStop: false,
  tasks: [],
  thought: "",
  response: "",
  taskStatus: 0,
  tip: "",
  multiAgent: { tasks: [] }
};

// 测试函数
export function testBankDataReportHandling() {
  console.log("测试银行数据报告流式数据处理...");
  
  // 第一次流式数据
  const result1 = combineData(mockBankDataReportStream, mockCurrentChat);
  console.log("第一次处理结果:", result1);
  
  // 第二次流式数据（追加内容）
  const mockStreamData2 = {
    ...mockBankDataReportStream,
    resultMap: {
      ...mockBankDataReportStream.resultMap,
      toolResult: {
        ...mockBankDataReportStream.resultMap.toolResult,
        toolResult: "\n\n### 数据趋势\n根据分析结果显示..."
      }
    }
  };
  
  const result2 = combineData(mockStreamData2, result1);
  console.log("第二次处理结果:", result2);
  
  // 最终数据（标记完成）
  const mockFinalData = {
    ...mockBankDataReportStream,
    resultMap: {
      ...mockBankDataReportStream.resultMap,
      isFinal: true,
      toolResult: {
        ...mockBankDataReportStream.resultMap.toolResult,
        toolResult: "\n\n### 结论\n分析完成。"
      }
    }
  };
  
  const finalResult = combineData(mockFinalData, result2);
  console.log("最终处理结果:", finalResult);
  
  return finalResult;
}

// 导出测试数据供其他地方使用
export { mockBankDataReportStream, mockCurrentChat };
